import React, {useCallback, useEffect, useState} from "react";
import {BlockStack, Checkbox, Modal, Text} from "@shopify/polaris";
import { useAuthenticatedFetch } from "../hooks";
import { useTranslation } from "react-i18next";
import { useAppBridge } from '@shopify/app-bridge-react';
import i18next from "i18next";

export default function TransferDocumentsModal({title, active, setActive, invoices, creditNotes, transactions}) {
  const handleChange = useCallback(() => setActive(!active), [active]);
  const authenticatedFetch = useAuthenticatedFetch();
  const shopify = useAppBridge();

  // Toast function using the new Toast API
  const showToast = (message, isError = false) => {
    if (shopify?.toast) {
      shopify.toast.show(message, { isError });
    }
  };

  const [selectedTypes, setSelectedTypes] = useState({
    invoices: invoices.length !== 0,
    creditNotes: creditNotes.length !== 0,
    transactions: transactions.length !== 0
  });

  useEffect(() => {
    setSelectedTypes({
      invoices: invoices.length !== 0,
      creditNotes: creditNotes.length !== 0,
      transactions: transactions.length !== 0
    });
  }, [invoices, creditNotes, transactions]);

  const handleCheckboxChange = (type) => {
    setSelectedTypes((prevState) => {
      return {
        ...prevState,
        [type]: !prevState[type]
      };
    });
  };
    const handleTransfer = async () => {
      const syncInfoIds = [
        ...selectedTypes.invoices ? invoices.map(invoice => invoice.sync_info_id) : [],
        ...selectedTypes.creditNotes ? creditNotes.map(creditNote => creditNote.sync_info_id) : [],
        ...selectedTypes.transactions ? transactions.map(transaction => transaction.sync_info_id) : []
      ];

      try {
        const response = await authenticatedFetch('/api/retry_skipped_transfers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ sync_info_ids: syncInfoIds })
        });

        if (!response.ok) {
          throw new Error('Network response was not ok');
        }

        const data = await response.json();
        showToast(i18next.t("transfer_log.modal.success"));
      } catch (error) {
        console.error('Transfer failed:', error);
      }
      setActive(false);
    };

  return (
    <Modal open={active}
           size={"small"}
           onClose={handleChange}
           title={title}
           primaryAction={{
             content: i18next.t("transfer_log.modal.action"), onAction: handleTransfer
           }}
           secondaryActions={[
             {content: i18next.t("transfer_log.modal.cancel"), onAction: handleChange}
           ]}
    >
      <Modal.Section>
        <BlockStack>
          <Checkbox
            disabled={invoices.length === 0}
            label={i18next.t("transfer_log.invoices")}
            checked={selectedTypes.invoices}
            onChange={() => handleCheckboxChange('invoices')}
          />
          <Checkbox
            disabled={creditNotes.length === 0}
            label={i18next.t("transfer_log.credit_notes")}
            checked={selectedTypes.creditNotes}
            onChange={() => handleCheckboxChange('creditNotes')}
          />
          <Checkbox
            disabled={transactions.length === 0}
            label={i18next.t("transfer_log.transactions")}
            checked={selectedTypes.transactions}
            onChange={() => handleCheckboxChange('transactions')}
          />
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
}
