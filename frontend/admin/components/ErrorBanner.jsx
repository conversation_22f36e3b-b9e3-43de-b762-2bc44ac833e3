import { Banner, Text } from "@shopify/polaris";
import { useState, useEffect } from 'react';
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

export default function ErrorBanner({transferHistoryData, onPrimaryActionClick}) {
    const [errorBanner, setErrorBanner] = useState(false);
    const { t } = useTranslation();
    const navigation = useNavigate();

    const handleSecondaryActionClick = () => {
        navigation(`/help`);
    };

    useEffect(() => {
        if (transferHistoryData) {
            setErrorBanner(transferHistoryData?.errors);
        }
    }, [transferHistoryData]);

    if (!errorBanner) { return null; }

    return (
        <div style={{ marginBottom: '20px' }}>
            <Banner
                title={t("transfer_log.banner_error.title")}
                tone={"warning"}
                action={{
                    content: t("transfer_log.banner_error.button"),
                    onAction: onPrimaryActionClick
                }}
                secondaryAction={{
                    content: t("transfer_log.banner_error.secondary_button"),
                    onAction: handleSecondaryActionClick
                }}
                onDismiss={() => setErrorBanner(false)}>
                <Text variant="headingXs" as="h6">
                    {t("transfer_log.banner_error.text")}
                </Text>
            </Banner>
        </div>
    );
}